<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspirational Quote | Wisdom Collection - quotese.com</title>
    <meta name="description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta name="keywords" content="inspirational quote, wisdom quote, famous saying, thought-provoking quote, life quote">
    <link rel="canonical" href="https://quotese.com/quotes/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Inspirational Quote | Wisdom Collection - quotese.com">
    <meta property="og:description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta property="og:image" content="https://quotese.com/images/og-image-quote.jpg">
    <meta property="og:url" content="https://quotese.com/quotes/">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@quotesecom">
    <meta name="twitter:title" content="Inspirational Quote | Wisdom Collection">
    <meta name="twitter:description" content="Explore this profound quote and its context. Discover wisdom, inspiration, and insights from history's most influential thinkers.">
    <meta name="twitter:image" content="https://quotese.com/images/og-image-quote.jpg">
    <!-- Tailwind CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;500;600;700&family=Noto+Sans:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
    <!-- Google Analytics -->
    <script src="/js/analytics.js?v=20250626"></script>
</head>
<body class="light-mode">
    <!-- 导航栏 (将由组件加载器加载) -->
    <header id="navigation-container" role="banner"></header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8" role="main">
        <!-- 面包屑导航 -->
        <div id="breadcrumb-container"></div>
        <!-- Quote Card -->
        <section class="mb-12 fade-in" id="quote-card-container">
            <div class="max-w-4xl mx-auto">
                <div class="relative p-6 sm:p-8 md:p-10 bg-gradient-to-br from-yellow-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 quote-marks">
                    <div class="relative z-10">
                        <h1 id="quote-content" class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-serif leading-relaxed mb-6 sm:mb-8">
                            "Quote content will be displayed here."
                        </h1>
                        <div class="flex items-center mt-6">
                            <div class="flex-shrink-0 mr-4">
                                <div class="w-12 h-12 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 border-2 border-yellow-400 dark:border-yellow-600">
                                    <span id="author-initial" class="text-sm font-bold">A</span>
                                </div>
                            </div>
                            <div>
                                <a id="author-link" href="#" class="font-semibold text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 transition-colors duration-300">Author Name</a>
                                <p id="source-text" class="text-sm text-gray-500 dark:text-gray-400">Source Name</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 flex flex-wrap gap-2" id="categories-container">
                        <!-- Categories will be added here -->
                    </div>
                    <div class="mt-8 flex justify-between items-center border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            <span id="quote-date">Date</span>
                        </div>
                        <div class="flex space-x-2">
                            <button id="share-button" class="p-2 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400 transition-colors duration-300 rounded-full hover:bg-yellow-50 dark:hover:bg-gray-700" title="Share">
                                <i class="fas fa-share-alt"></i>
                            </button>
                            <button id="copy-button" class="p-2 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400 transition-colors duration-300 rounded-full hover:bg-yellow-50 dark:hover:bg-gray-700" title="Copy to clipboard">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Content Grid (Left-Right Layout) -->
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Left Column (Related Quotes) -->
            <section class="lg:w-2/3">
                <h2 class="text-2xl font-bold mb-6 flex items-center">
                    <i class="fas fa-quote-right text-yellow-500 mr-2" aria-hidden="true"></i>
                    More Quotes by <span id="author-name-heading">this Author</span>
                </h2>

                <div id="related-quotes-container">
                    <!-- Related quotes will be loaded here -->
                    <div class="flex justify-center py-12">
                        <div class="loading-spinner" role="status">
                            <span class="sr-only">Loading quotes...</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Right Column (Sidebar) -->
            <aside class="lg:w-1/3" role="complementary" aria-label="Sidebar">
                <div id="popular-topics-container">
                    <!-- Popular topics component will be loaded here -->
                </div>
            </aside>
        </div>
    </main>

    <!-- Footer (will be loaded by component loader) -->
    <footer id="footer-container" role="contentinfo"></footer>

    <!-- JavaScript -->
    <!-- Debug Script -->
    <script src="/js/debug.js?v=20250626"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js?v=20250626"></script>

    <!-- Mock Data -->
    <script src="/js/mock-data.js?v=20250626"></script>

    <!-- API Client -->
    <script src="/js/api-client.js?v=20250626"></script>

    <!-- Core Modules -->
    <script src="/js/theme.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/seo-manager.js?v=20250626"></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/mobile-menu.js?v=20250626"></script>
    <script src="/js/components/breadcrumb.js?v=20250626"></script>
    <script src="/js/social-meta.js?v=20250626"></script>

    <!-- Global Fix Script -->
    <script src="/js/global-fix.js?v=20250626"></script>

    <!-- Page Specific Script -->
    <script src="/js/pages/quote.js?v=20250628"></script>
</body>
</html>
