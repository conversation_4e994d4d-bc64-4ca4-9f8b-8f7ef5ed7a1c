当前工作目录: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend
启动语义化URL服务器在端口 8083...
服务器地址: http://localhost:8083

支持的语义化URL模式:
- /authors/ -> authors.html
- /authors/{slug}/ -> author.html
- /authors/{slug}/quotes/ -> author.html
- /categories/ -> categories.html
- /categories/{slug}/ -> category.html
- /categories/{slug}/quotes/ -> category.html
- /sources/ -> sources.html
- /sources/{slug}/ -> source.html
- /quotes/ -> quotes.html
- /quotes/{id}/ -> quote.html
- /search/ -> search.html

按 Ctrl+C 停止服务器
🔍 收到GET请求: /?use-production-api=true
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET /?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 200 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/499279/
🔍 解析路径: /quotes/499279/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499279',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/499279/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499279/ HTTP/1.1" 200 -
🔍 收到GET请求: /test-quote-card-comprehensive.html
🔍 解析路径: /test-quote-card-comprehensive.html
❌ 未匹配到任何语义化URL模式: /test-quote-card-comprehensive.html
[127.0.0.1] "GET /test-quote-card-comprehensive.html HTTP/1.1" 200 -
🔍 收到GET请求: /?use-production-api=true
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET /?use-production-api=true HTTP/1.1" 304 -
🔍 收到GET请求: /js/config.js?v=20250623
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 304 -
🔍 收到GET请求: /categories/intelligence/?use-production-api=true
🔍 解析路径: /categories/intelligence/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('intelligence',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/category.html
   ✅ 重写路径为: /category.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /categories/intelligence/
重定向到 category.html
[127.0.0.1] "GET /categories/intelligence/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /js/quote-card-click-fix.js?v=20250628
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] "GET /js/quote-card-click-fix.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /authors/pearl-zhu/?use-production-api=true
🔍 解析路径: /authors/pearl-zhu/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('pearl-zhu',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/author.html
   ✅ 重写路径为: /author.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /authors/pearl-zhu/
重定向到 author.html
[127.0.0.1] "GET /authors/pearl-zhu/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /sources/1984/?use-production-api=true
🔍 解析路径: /sources/1984/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('1984',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/source.html
   ✅ 重写路径为: /source.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /sources/1984/
重定向到 source.html
[127.0.0.1] "GET /sources/1984/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /.well-known/appspecific/com.chrome.devtools.json
🔍 解析路径: /.well-known/appspecific/com.chrome.devtools.json
❌ 未匹配到任何语义化URL模式: /.well-known/appspecific/com.chrome.devtools.json
未匹配的路径，可能需要添加新的URL模式: /.well-known/appspecific/com.chrome.devtools.json
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 -
🔍 收到GET请求: /quotes/499310/
🔍 解析路径: /quotes/499310/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499310',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/499310/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499310/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/497987/
🔍 解析路径: /quotes/497987/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('497987',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/497987/
重定向到 quote.html
[127.0.0.1] "GET /quotes/497987/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/395096/
🔍 解析路径: /quotes/395096/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('395096',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/395096/
重定向到 quote.html
[127.0.0.1] "GET /quotes/395096/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 304 -
🔍 收到GET请求: /.well-known/appspecific/com.chrome.devtools.json
🔍 解析路径: /.well-known/appspecific/com.chrome.devtools.json
❌ 未匹配到任何语义化URL模式: /.well-known/appspecific/com.chrome.devtools.json
未匹配的路径，可能需要添加新的URL模式: /.well-known/appspecific/com.chrome.devtools.json
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 -
🔍 收到GET请求: /authors/charles-bukowski/
🔍 解析路径: /authors/charles-bukowski/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('charles-bukowski',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/author.html
   ✅ 重写路径为: /author.html
未匹配的路径，可能需要添加新的URL模式: /authors/charles-bukowski/
重定向到 author.html
[127.0.0.1] "GET /authors/charles-bukowski/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/quote-card-click-fix.js?v=20250628
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] "GET /js/quote-card-click-fix.js?v=20250628 HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/495494/
🔍 解析路径: /quotes/495494/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('495494',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/495494/
重定向到 quote.html
[127.0.0.1] "GET /quotes/495494/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /test-quote-detail-debug.html?use-production-api=true
🔍 解析路径: /test-quote-detail-debug.html
❌ 未匹配到任何语义化URL模式: /test-quote-detail-debug.html
[127.0.0.1] "GET /test-quote-detail-debug.html?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /js/config.js
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js HTTP/1.1" 304 -
🔍 收到GET请求: /js/api-client.js
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /.well-known/appspecific/com.chrome.devtools.json
🔍 解析路径: /.well-known/appspecific/com.chrome.devtools.json
❌ 未匹配到任何语义化URL模式: /.well-known/appspecific/com.chrome.devtools.json
未匹配的路径，可能需要添加新的URL模式: /.well-known/appspecific/com.chrome.devtools.json
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 304 -
[127.0.0.1] code 400, message Bad HTTP/0.9 request type ('\x16\x03\x01\x01j\x01\x00\x01f\x03\x03§]£P\x00È~®¿\x80HS¯+T»\x80\x95è\x95ÒÛ\x10ç\x05S3\x8cßÁ\x9fj')
[127.0.0.1] "j f§]£P È~®¿HS¯+T»èÒÛçS3ßÁj " 400 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /test-quote-detail-final.html?use-production-api=true
🔍 解析路径: /test-quote-detail-final.html
❌ 未匹配到任何语义化URL模式: /test-quote-detail-final.html
[127.0.0.1] "GET /test-quote-detail-final.html?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /components/navigation.html
🔍 解析路径: /components/navigation.html
❌ 未匹配到任何语义化URL模式: /components/navigation.html
✅ 静态文件请求: /components/navigation.html
[127.0.0.1] "GET /components/navigation.html HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/navigation.js
🔍 解析路径: /js/components/navigation.js
❌ 未匹配到任何语义化URL模式: /js/components/navigation.js
✅ 静态文件请求: /js/components/navigation.js
[127.0.0.1] "GET /js/components/navigation.js HTTP/1.1" 304 -
🔍 收到GET请求: /components/breadcrumb.html
🔍 解析路径: /components/breadcrumb.html
❌ 未匹配到任何语义化URL模式: /components/breadcrumb.html
✅ 静态文件请求: /components/breadcrumb.html
[127.0.0.1] "GET /components/breadcrumb.html HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/499001/
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/499001/?use-production-api=true
🔍 解析路径: /quotes/499001/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499001',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html?use-production-api=true
未匹配的路径，可能需要添加新的URL模式: /quotes/499001/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499001/?use-production-api=true HTTP/1.1" 304 -
🔍 收到GET请求: /quotes
🔍 解析路径: /quotes
❌ 未匹配到任何语义化URL模式: /quotes
未匹配的路径，可能需要添加新的URL模式: /quotes
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /quotes HTTP/1.1" 404 -
🔍 收到GET请求: /categories/belief/
🔍 解析路径: /categories/belief/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('belief',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/belief/
重定向到 category.html
[127.0.0.1] "GET /categories/belief/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/quote-card-click-fix.js?v=20250628
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] "GET /js/quote-card-click-fix.js?v=20250628 HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/498058/
🔍 解析路径: /quotes/498058/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('498058',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0628_V4/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/498058/
重定向到 quote.html
[127.0.0.1] "GET /quotes/498058/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -